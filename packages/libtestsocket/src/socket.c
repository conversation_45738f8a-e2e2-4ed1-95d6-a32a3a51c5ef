/**
 * @file socket.c
 * @brief Socket通信模块实现
 */

#include <errno.h>
#include <json-c/json_object.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fcntl.h>
#include <json-c/json.h>
#include "../include/libtestsocket.h"

#define SOCKET_PATH "/var/run/testd/testd.socket"
#define BUFFER_SIZE 4096

static int sockfd = -1;
static char socket_buffer[BUFFER_SIZE];
static int socket_pending_bytes = -1, socket_buffer_pos = 0;
test_mode_t _test_current_mode = TEST_MODE_NORMAL;
mock_func_entry_t* _test_global_mock_funcs = NULL;
int _test_run_flag = 1;
void *_test_bkpt_addrs[32];
int _test_dynamic_mode = 0;



/**
 * @brief 连接到测试控制Socket
 * @return 成功返回0，失败返回-1
 */
static int connect_to_socket() {
    struct sockaddr_un addr;
    
    if ((sockfd = socket(AF_UNIX, SOCK_STREAM, 0)) == -1) {
        perror("socket error");
        return -1;
    }

    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, SOCKET_PATH, sizeof(addr.sun_path)-1);

    if (connect(sockfd, (struct sockaddr*)&addr, sizeof(addr)) == -1) {
        close(sockfd);
        sockfd = -1;
        return -1;
    }
    
    if (fcntl(sockfd, F_SETFL, O_NONBLOCK) == -1) {
        perror("fcntl error");
        close(sockfd);
        sockfd = -1;
        return -1;
    }

    return 0;
}

static int send_json_message(json_object* json_msg) {
    if (sockfd == -1) return -1;

    const char* json_str = json_object_to_json_string(json_msg);
    size_t len = strlen(json_str);

    /*
        format:

        <pack_length>\n<content>\n
    */
    char buffer[128];
    snprintf(buffer, sizeof(buffer), "%zu\n", len);
    ssize_t written = write(sockfd, buffer, strlen(buffer));
    if (written < 0 || (size_t)written != strlen(buffer)) {
        perror("write error");
        return -1;
    }

    written = write(sockfd, json_str, len);
    if (written < 0 || (size_t)written != len) {
        perror("write error");
        return -1;
    }

    buffer[0] = '\n';
    written = write(sockfd, buffer, 1);
    fsync(sockfd);
    
    if (written < 0 || (size_t)written != 1) {
        perror("write error");
        return -1;
    }

    return 0;
}

static int receive_socket_message() {
    if (sockfd == -1) return -1;
    if (socket_buffer_pos < 0)socket_buffer_pos = 0;

    ssize_t n = read(sockfd, socket_buffer + socket_buffer_pos, BUFFER_SIZE - 1 - socket_buffer_pos);
    if (n < 0) {
        if (errno != EAGAIN) {
            perror("read error");
            close(sockfd);
            sockfd = -1;
        }
        return -1;
    }
    if (n == 0) {
        // 连接关闭
        close(sockfd);
        sockfd = -1;
        return -1;
    }
    socket_buffer_pos += n;
    socket_buffer[socket_buffer_pos] = '\0';
    return 0;

}

static int receive_json_message(json_object** json_obj) {

    /*
        format:

        <pack_length>\n<content>\n
    */
    if (json_obj == NULL) return -1;

    if (socket_pending_bytes <= 0)
    {
        char *p = strchr(socket_buffer, '\n');
        if (!p) return -1;
        *p = '\0';
        socket_pending_bytes = atoi(socket_buffer);
        int current_content_length = socket_buffer_pos - (p - socket_buffer);
        memmove(socket_buffer, p + 1, current_content_length);
        socket_buffer_pos = current_content_length;
        if (socket_pending_bytes == 0)
        {
            socket_pending_bytes = -1;
            socket_buffer_pos = 0;
            return -1;
        }
        socket_pending_bytes += 1;
    }
    
    if (socket_pending_bytes <= socket_buffer_pos)
    {
        socket_buffer[socket_pending_bytes - 1] = '\0';
        *json_obj = json_tokener_parse(socket_buffer);
        memmove(socket_buffer, socket_buffer + socket_pending_bytes, socket_buffer_pos - socket_pending_bytes);
        socket_buffer_pos -= socket_pending_bytes;
        socket_pending_bytes = -1;
        socket_buffer_pos -= 1;
        return 0;
    }
    return -1;
}

static int handle_memory_debug(json_object* debug_cmd) {
    json_object *action_obj, *addr_obj, *bytes_obj, *data_obj;
    const char *action;
    void *addr;
    size_t bytes;

    if (!json_object_object_get_ex(debug_cmd, "action", &action_obj) ||
        !json_object_object_get_ex(debug_cmd, "addr", &addr_obj) ||
        !json_object_object_get_ex(debug_cmd, "bytes", &bytes_obj)) {
        return -1;
    }

    action = json_object_get_string(action_obj);
    addr = (void*)strtoul(json_object_get_string(addr_obj), NULL, 16);
    bytes = json_object_get_int(bytes_obj);

    if (strcmp(action, "read") == 0) {
        char *buffer = malloc(bytes+1);
        if (!buffer) return -1;

        memcpy(buffer, addr, bytes);
        buffer[bytes] = '\0';
        _test_send_info("memory_read", buffer);
        free(buffer);
        return 0;
    }
    else if (strcmp(action, "write") == 0 &&
             json_object_object_get_ex(debug_cmd, "data", &data_obj)) {
        const char *data = json_object_get_string(data_obj);
        memcpy(addr, data, bytes);
        return 0;
    }

    return -1;
}

mock_func_entry_t* _test_find_mock_func(const char* name) {
    if (!_test_global_mock_funcs) return NULL;
    
    for (mock_func_entry_t* entry = _test_global_mock_funcs; entry; entry = entry->next) {
        if (strcmp(entry->name, name) == 0) {
            return entry;
        }
    }
    return NULL;
}

mock_func_entry_t* _test_find_mock_func_by_ptr(void *ptr) {
    if (!_test_global_mock_funcs) return NULL;
    
    for (mock_func_entry_t* entry = _test_global_mock_funcs; entry; entry = entry->next) {
        if (entry->real_func_ptr == ptr) {
            return entry;
        }
    }
    return NULL;
}

mock_func_entry_t *_test_get_mock_func_head(void) {
    return _test_global_mock_funcs;
}

static int mock_buffer_write(mock_func_entry_t* entry, int param_index, const char *type, json_object* values_obj, int reset_flag) {
    static const char* type_names[] = {
        "none","int", "string", "ptr_in", "ptr_out", "ptr_static", "ptr_indirect"
    };
    param_type_t type_enum = -1;
    if (!entry || !values_obj) return -1;
    int val_length = json_object_array_length(values_obj);
    if (val_length == 0) return -1;
    for (int i = 0; i < 6; i++) {
        if (strcmp(type, type_names[i]) == 0) {
            type_enum = i;
            break;
        }
    }
    if ((int)type_enum == -1) return -1;
    if (type_enum != entry->param_types[param_index]) return -1;
    if (reset_flag) {
        *(entry->buffer_wp_vars[param_index]) = 0;
    }
    for (int i = 0; i < val_length; i++) {
        json_object* value = json_object_array_get_idx(values_obj, i);
        if (type_enum == PARAM_INT) {
            int* ret_buf = (int*)entry->buffer_addrs[param_index];
            ret_buf[*(entry->buffer_wp_vars[param_index])] = json_object_get_int(value);
        }else if(type_enum == PARAM_PTR_OUT || type_enum == PARAM_STRING){
            const char* str = json_object_get_string(value);
            char* buf = (char*)entry->buffer_addrs[param_index] +
                        *(entry->buffer_wp_vars[param_index]) * entry->buffer_sizes[param_index];
            strncpy(buf, str, entry->buffer_sizes[param_index]);
        }
        *(entry->buffer_wp_vars[param_index]) += 1;
        if (*(entry->buffer_wp_vars[param_index]) >= entry->buffer_counts[param_index]) {
            fprintf(stderr, "buffer overflow\n");
            exit(1);
        }
    }
    

    return 0;
}

static int alloc_buffer(mock_func_entry_t* entry, int param_index, const char* name, param_type_t type, size_t size, size_t count) {
    if (entry->buffer_addrs[param_index] != NULL) return 0;
    entry->buffer_addrs[param_index] = malloc(size * count);
    entry->buffer_id_vars[param_index] = calloc(1,sizeof(size_t));
    entry->buffer_wp_vars[param_index] = calloc(1,sizeof(size_t));
    entry->buffer_sizes[param_index] = size;
    entry->buffer_counts[param_index] = count;
    entry->param_types[param_index] = type;
    if (name != NULL)
        entry->param_names[param_index] = strdup(name);
    return 0;
}

static int handle_mock_data(json_object* mocks) {
    if (!mocks) return -1;
    //printf("mocks: %s\n", json_object_to_json_string(mocks));

    int array_len = json_object_array_length(mocks);
    for (int i = 0; i < array_len; i++) {
        json_object* mock_item = json_object_array_get_idx(mocks, i);
        json_object *func_obj, *return_obj, *arg_count_obj, *return_list_obj;
        
        if (!json_object_object_get_ex(mock_item, "function", &func_obj)) {
            continue;
        }

        int lua_arg_count = -1, lua_ret_count = -1;
        if (json_object_object_get_ex(mock_item, "arg_count", &arg_count_obj)) {
            lua_arg_count = json_object_get_int(arg_count_obj);
            lua_ret_count = 0;
        }
        if (json_object_object_get_ex(mock_item, "return_list", &return_list_obj)) {
            lua_ret_count = json_object_array_length(return_list_obj);
        }

        const char* func_name = json_object_get_string(func_obj);
        mock_func_entry_t* entry = _test_find_mock_func(func_name);
        if (!entry && _test_dynamic_mode) {
            if (lua_arg_count < 0) {
                // Shell mode
                entry = calloc(1, sizeof(mock_func_entry_t));
                entry->name = strdup(func_name);
                alloc_buffer(entry, 0, NULL, PARAM_INT, sizeof(int), DYNAMIC_MODE_BUFFER_COUNT); // for return value
                alloc_buffer(entry, 1, "stdout", PARAM_PTR_OUT, DYNAMIC_MODE_BUFFER_SIZE, DYNAMIC_MODE_BUFFER_COUNT); // for stdout
                alloc_buffer(entry, 2, "stderr", PARAM_PTR_OUT, DYNAMIC_MODE_BUFFER_SIZE, DYNAMIC_MODE_BUFFER_COUNT); // for stderr

                entry->next = _test_global_mock_funcs;
                _test_global_mock_funcs = entry;
            }else{
                // Lua mode
                if (lua_arg_count + lua_ret_count > MAX_PARAM_COUNT) {
                    printf("too many params\n");
                    return -1;
                }
                entry = calloc(1, sizeof(mock_func_entry_t));
                entry->name = strdup(func_name);
                entry->param_count = lua_arg_count;
                // TODO: 处理参数
                for (int i = lua_arg_count; i < lua_arg_count + lua_ret_count; i++) {
                    json_object *ret_item = json_object_array_get_idx(return_list_obj, i - lua_arg_count);
                    json_object *type_obj;
                    if (json_object_object_get_ex(ret_item, "type", &type_obj)) {
                        const char *type_str = json_object_get_string(type_obj);
                        if (strcmp(type_str, "int") == 0) {
                            entry->param_types[i] = PARAM_INT;
                            alloc_buffer(entry, i, NULL, PARAM_INT, sizeof(int), DYNAMIC_MODE_BUFFER_COUNT);
                        } else if (strcmp(type_str, "string") == 0) {
                            entry->param_types[i] = PARAM_STRING;
                            alloc_buffer(entry, i, NULL, PARAM_STRING, DYNAMIC_MODE_BUFFER_SIZE, DYNAMIC_MODE_BUFFER_COUNT);
                        }
                    }else{
                        printf("no return type\n");
                        return -1;
                    }
                }
                entry->next = _test_global_mock_funcs;
                _test_global_mock_funcs = entry;
            }
        }
        int reset_flag = 1;

        // printf("mock data: %s\n", json_object_to_json_string(mock_item));
        if (!entry) return -1;
        // 处理返回值
        if (json_object_object_get_ex(mock_item, "return", &return_obj)) {
            json_object *type_obj, *values_obj, *reset_obj;
            int ret_param_index = 0;
            for (int i = 0; i < MAX_PARAM_COUNT; i++) {
                if (entry->param_types[i] != PARAM_NONE && entry->param_names[i] == NULL) {
                    ret_param_index = i;
                    break;
                }
            }
            if (ret_param_index == MAX_PARAM_COUNT) {
                printf("no return param\n");
                return -1;
            }
            if (json_object_object_get_ex(return_obj, "reset", &reset_obj)) {
                reset_flag = json_object_get_int(reset_obj);
            }
            if (json_object_object_get_ex(return_obj, "type", &type_obj) &&
                json_object_object_get_ex(return_obj, "values", &values_obj)) {
                    mock_buffer_write(entry, ret_param_index, json_object_get_string(type_obj), values_obj, reset_flag);
            }else{
                printf("no return type\n");
                return -1;
            }
        }else if (return_list_obj){
            for (int i = 0; i < lua_ret_count; i++) {
                json_object *ret_item = json_object_array_get_idx(return_list_obj, i);
                json_object *type_obj, *values_obj, *reset_obj;
                if (json_object_object_get_ex(ret_item, "reset", &reset_obj)) {
                    reset_flag = json_object_get_int(reset_obj);
                }
                if (json_object_object_get_ex(ret_item, "type", &type_obj) &&
                    json_object_object_get_ex(ret_item, "values", &values_obj)) {
                    mock_buffer_write(entry, lua_arg_count + i, json_object_get_string(type_obj), values_obj, reset_flag);
                }else{
                    printf("no return type\n");
                    return -1;
                }
            }
        }else{
            printf("no return\n");
            return -1;
        }

        // 处理参数
        json_object* args_obj;
        if (json_object_object_get_ex(mock_item, "args", &args_obj)) {
            for (size_t i = 0; i < json_object_array_length(args_obj); i++) {
                json_object* arg_item = json_object_array_get_idx(args_obj, i);
                json_object *name_obj, *type_obj, *values_obj, *reset_obj;
                const char *name_str;
                int j;

                if (json_object_object_get_ex(arg_item, "name", &name_obj) &&
                    json_object_object_get_ex(arg_item, "type", &type_obj) &&
                    json_object_object_get_ex(arg_item, "values", &values_obj)) {
                    if (json_object_object_get_ex(arg_item, "reset", &reset_obj)) {
                        reset_flag = json_object_get_int(reset_obj);
                    } else {
                        reset_flag = 1;
                    }
                    name_str = json_object_get_string(name_obj);
                    for (j = 0; j < MAX_PARAM_COUNT; j++) {
                        if (entry->param_names[j] && strcmp(entry->param_names[j], name_str) == 0) {
                            mock_buffer_write(entry, j, json_object_get_string(type_obj), values_obj, reset_flag);
                            break;
                        }
                    }
                    if (j == MAX_PARAM_COUNT) {
                        printf("no such param: %s\n", json_object_get_string(name_obj));
                        return -1;
                    }
                }
            }
        }else if (lua_arg_count != 0){
            printf("no args\n");
            return -1;
        }
    }
    return 0;
}

static int update_aspects_status(json_object* aspects) {    
    // 默认禁用所有切面
    for (mock_func_entry_t* entry = _test_global_mock_funcs; entry; entry = entry->next) {
        entry->is_active = 0;
    }
    
    if (!aspects) return -3;
    
    size_t aspect_count = json_object_array_length(aspects);
    for (size_t i = 0; i < aspect_count; i++) {
        const char* aspect = json_object_get_string(json_object_array_get_idx(aspects, i));
        for (mock_func_entry_t* entry = _test_global_mock_funcs; entry; entry = entry->next) {
            if (entry->aspect && strcmp(entry->aspect, aspect) == 0) {
                entry->is_active = 1;
            }
        }
    }
    return 0;
}

static int handle_bkpt(json_object* bkpt_obj) {
    json_object *func_obj_array;
    int bkpt_count = 0;

    if (!json_object_object_get_ex(bkpt_obj, "functions", &func_obj_array)) {
        return -1;
    }

    bkpt_count = json_object_array_length(func_obj_array);
    if (bkpt_count > 32 - 1) { //保留一个NULL
        return -1;
    }

    for (int i = 0; i < bkpt_count; i++) {
        json_object* func_obj = json_object_array_get_idx(func_obj_array, i);
        const char* func_name = json_object_get_string(func_obj);
        mock_func_entry_t* entry = _test_find_mock_func(func_name);
        if (entry) {
            _test_bkpt_addrs[i] = entry->real_func_ptr;
        }else{
            _test_bkpt_addrs[i] = NULL;
        }
    }
    for (int i = bkpt_count; i < 32; i++) {
        _test_bkpt_addrs[i] = NULL;
    }

    return 0;
}

static int handle_command(json_object* cmd) {
    json_object *mode_obj = NULL, *debug_obj = NULL, *mocks_obj = NULL, *aspects_obj = NULL, *bkpt_obj = NULL;
    int ret = 0;
    int handled = 0;
    // 处理切面配置
    if (json_object_object_get_ex(cmd, "aspects", &aspects_obj)) {
        ret = update_aspects_status(aspects_obj);
        handled = 1;
    }

    // 处理模式切换
    if (json_object_object_get_ex(cmd, "mode", &mode_obj)) {
        const char* mode_str = json_object_get_string(mode_obj);
        if (strcmp(mode_str, "normal") == 0) {
            _test_current_mode = TEST_MODE_NORMAL;
            ret = 0;
        } else if (strcmp(mode_str, "mock") == 0) {
            _test_current_mode = TEST_MODE_MOCK;
            ret = 0;
        } else if (strcmp(mode_str, "collect") == 0) {
            _test_current_mode = TEST_MODE_COLLECT;
            ret = 0;
        } else if (strcmp(mode_str, "debug") == 0) {
            _test_current_mode = TEST_MODE_DEBUG;
            ret = 0;
        } else {
            ret = -2;
        }
        
        handled = 1;
    }

    // 处理mock数据
    if (ret == 0 && json_object_object_get_ex(cmd, "mocks", &mocks_obj)) {
        ret = handle_mock_data(mocks_obj);
        handled = 1;
    }

    // 处理debug命令
    if (ret == 0 &&
        json_object_object_get_ex(cmd, "debug", &debug_obj)) {
        ret = handle_memory_debug(debug_obj);
        handled = 1;
        
    }

    // 处理bkpt命令
    if (ret == 0 &&
        json_object_object_get_ex(cmd, "breakpoints", &bkpt_obj)) {
        ret = handle_bkpt(bkpt_obj);
        handled = 1;
    }

    // 处理动作
    json_object* action_obj = NULL;
    if (json_object_object_get_ex(cmd, "action", &action_obj)) {
        const char* action = json_object_get_string(action_obj);
        
        if (strcmp(action, "stop") == 0) {
            _test_run_flag = 0;
        }else if(strcmp(action, "run") == 0){
            _test_run_flag = 1;
        }
        handled = 1;
    }


    if (handled)
    {
        // 发送响应
        json_object* resp = json_object_new_object();
        json_object_object_add(resp, "id", json_object_get(json_object_object_get(cmd, "id")));
        json_object_object_add(resp, "type", json_object_new_string("response"));
        json_object_object_add(resp, "result", json_object_new_int(ret));
        if (ret != 0) {
            json_object_object_add(resp, "error", json_object_new_string(strerror(errno)));
        }
        send_json_message(resp);
        json_object_put(resp);

    }else{
        // 默认返回错误响应
        json_object* resp = json_object_new_object();
        json_object_object_add(resp, "id", json_object_get(json_object_object_get(cmd, "id")));
        json_object_object_add(resp, "type", json_object_new_string("response"));
        json_object_object_add(resp, "result", json_object_new_int(-1));
        json_object_object_add(resp, "error", json_object_new_string("invalid command"));
        send_json_message(resp);
        json_object_put(resp);

    }
    
    return ret;
}

int _test_init(const char* program_name, mock_func_entry_t* mock_funcs) {
    _test_global_mock_funcs = mock_funcs;
    _test_dynamic_mode = mock_funcs == NULL;
    
    if (connect_to_socket() != 0) {
        _test_current_mode = TEST_MODE_NORMAL;
        return 0;
    }

    json_object* init_msg = json_object_new_object();
    json_object_object_add(init_msg, "type", json_object_new_string("init"));
    
    json_object* data_obj = json_object_new_object();
    json_object_object_add(data_obj, "pid", json_object_new_int(getpid()));
    json_object_object_add(data_obj, "program", json_object_new_string(program_name));
    json_object_object_add(init_msg, "data", data_obj);

    if (send_json_message(init_msg) != 0) {
        json_object_put(init_msg);
        _test_current_mode = TEST_MODE_NORMAL;
        return 0;
    }
    
    json_object_put(init_msg);

    // 等待控制端命令
    #define WAIT_TIMEOUT_MS 500000
    struct timeval start, now;
    gettimeofday(&start, NULL);

    _test_run_flag = 0;
    while (!_test_run_flag) {
        // 检查超时
        gettimeofday(&now, NULL);
        long elapsed = (now.tv_sec - start.tv_sec) * 1000 +
                      (now.tv_usec - start.tv_usec) / 1000;
        if (elapsed > WAIT_TIMEOUT_MS) {
            _test_current_mode = TEST_MODE_NORMAL;
            break;
        }

        json_object* msg;
        receive_socket_message();
        if (receive_json_message(&msg) != 0) continue;
        // 处理action命令
        handle_command(msg);
        json_object_put(msg);
        if (sockfd < 0){
            // 连接关闭
            printf("connection closed\n");
            exit(0);
        }
    }
    return 0;
}

void _test_process_event() {
    if (sockfd == -1) return;

    json_object* msg;
    do{

        receive_socket_message();
        if (receive_json_message(&msg) != 0) continue;

        json_object* type_obj = NULL;
        if (json_object_object_get_ex(msg, "type", &type_obj)) {
            const char* type = json_object_get_string(type_obj);
            if (strcmp(type, "command") == 0) {
                handle_command(msg);
            }
        }

        json_object_put(msg);
    }while(!_test_run_flag);
}

int _test_send_info(const char* type, const char* message) {
    if (sockfd == -1) return -1;

    json_object* msg = json_object_new_object();
    json_object_object_add(msg, "type", json_object_new_string(type));
    json_object_object_add(msg, "data", json_object_new_string(message));

    int ret = send_json_message(msg);
    json_object_put(msg);
    return ret;
}

int _test_send_log(const char* log_type, const char* message) {
    if (sockfd == -1) return -1;

    json_object* msg = json_object_new_object();
    json_object_object_add(msg, "type", json_object_new_string("log"));
    json_object_object_add(msg, "log_type", json_object_new_string(log_type));
    json_object_object_add(msg, "data", json_object_new_string(message));

    int ret = send_json_message(msg);
    json_object_put(msg);
    return ret;
}


void _test_cleanup() {
    if (sockfd != -1) {
        close(sockfd);
        sockfd = -1;
    }
}