{"name": "DNS解析测试", "description": "测试DNS解析功能", "setup": [{"action": "exec", "command": "LD_LIBRARY_PATH=/data/git/testd/build/libtestsocket/bin /data/git/testd/build/example_c_program/bin/example_client", "wait_ms": 2000}, {"action": "wait_attach", "processes": ["example_c_program"]}], "cases": [{"name": "DNS解析", "targets": [{"process": "example_c_program", "aspects": ["resolve_aspect"], "breakpoints": {"functions": ["network_connect"]}, "mocks": [{"function": "network_resolve", "return": {"type": "int", "values": [0]}, "args": [{"name": "ip", "type": "ptr_out", "values": ["***********"]}]}]}], "verify": {"expect_debug_log": {"type": "result_ip", "message": "***********"}}}], "cleanup": [{"kill-target": "example_c_program"}]}