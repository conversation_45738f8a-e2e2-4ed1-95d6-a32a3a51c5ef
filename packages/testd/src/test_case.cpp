/**
 * @file test_case.cpp
 * @brief 测试用例管理模块实现 - C++11版本
 */

#include "test_case.hpp"
#include "context.hpp"
#include "json_protocol.hpp"
#include <iostream>
#include <fstream>
#include <cstring>
#include <json-c/json_object.h>
#include <memory>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <uuid/uuid.h>
#include <sys/wait.h>
#include <spawn.h>
#include "socket.hpp"

namespace testd {

// TestCase类实现
TestCase::TestCase(const std::string& name, const std::string& description, Private)
    : Task(name, description, Private()), setup_obj(nullptr), cases_obj(nullptr), cleanup_obj(nullptr),
      targetPid(0), caseState(0) ,waitTimerFd(-1) {
}

TestCase::~TestCase() {
    if (setup_obj) {
        json_object_put(setup_obj);
    }
    
    if (cases_obj) {
        json_object_put(cases_obj);
    }
    
    if (cleanup_obj) {
        json_object_put(cleanup_obj);
    }
}

std::shared_ptr<TestCase> TestCase::loadFromFile(const std::string& filePath) {
    // 读取文件内容
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filePath << std::endl;
        return nullptr;
    }
    
    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();
    
    // 解析JSON
    struct json_object* jsonObj = json_tokener_parse(content.c_str());
    if (!jsonObj) {
        std::cerr << "Failed to parse JSON: " << filePath << std::endl;
        return nullptr;
    }
    
    // 获取测试用例名称和描述
    struct json_object* nameObj;
    struct json_object* descObj;
    std::string name;
    std::string description;
    
    if (json_object_object_get_ex(jsonObj, "name", &nameObj)) {
        name = json_object_get_string(nameObj);
    } else {
        std::cerr << "Missing 'name' field in test case file" << std::endl;
        json_object_put(jsonObj);
        return nullptr;
    }
    
    if (json_object_object_get_ex(jsonObj, "description", &descObj)) {
        description = json_object_get_string(descObj);
    }
    
    // 创建测试用例对象
    auto testCase = std::make_shared<TestCase>(name, description, Private());
    
    // 获取setup、cases和cleanup字段
    struct json_object* setupObj;
    struct json_object* casesObj;
    struct json_object* cleanupObj;
    
    std::shared_ptr<TestSetupTask> setupTask;
    if (json_object_object_get_ex(jsonObj, "setup", &setupObj)) {
        testCase->setSetup(setupObj);

        // 创建setup任务
        setupTask = TestSetupTask::create("Setup", "", setupObj);
        testCase->addSubTask(setupTask);
    }
    
    if (json_object_object_get_ex(jsonObj, "cases", &casesObj)) 
    {
        testCase->setCases(casesObj);
        // 创建cases任务
        auto casesTask = TestCaseListTask::create("Cases", "", casesObj);
        if (setupObj) {
            casesTask->setAfterTask(setupTask);
        }
        testCase->addSubTask(casesTask);
        testCase->casesTask = casesTask;
    } else {
        std::cerr << "Missing 'cases' field in test case file" << std::endl;
        json_object_put(jsonObj);
        return nullptr;
    }
    
    if (json_object_object_get_ex(jsonObj, "cleanup", &cleanupObj)) {
        testCase->setCleanup(cleanupObj);
        //TODO
    }
    
    // 释放JSON对象
    json_object_put(jsonObj);
    
    return testCase;
}

int TestCase::executeCustom() {
    if (state == State::INIT) {
        // 生成测试ID
        testId = generateTestId();
        result = std::make_shared<TestResult>(testId);
        
        return 0;
    }
    if (state == State::RUNNING) {
        result->parseCaseListTask(casesTask.lock());
        return 0;
    }
    return 0;
}

std::string TestCase::generateTestId() {
    uuid_t uuid;
    char uuid_str[37];
    
    uuid_generate(uuid);
    uuid_unparse_lower(uuid, uuid_str);
    
    return std::string(uuid_str);
}

void TestCase::setSetup(struct json_object* s) {
    if (setup_obj) {
        json_object_put(setup_obj);
    }
    setup_obj = s;
    if (setup_obj) {
        json_object_get(setup_obj);
    }
}

void TestCase::setCases(struct json_object* c) {
    if (cases_obj) {
        json_object_put(cases_obj);
    }
    cases_obj = c;
    if (cases_obj) {
        json_object_get(cases_obj);
    }
}

void TestCase::setCleanup(struct json_object* c) {
    if (cleanup_obj) {
        json_object_put(cleanup_obj);
    }
    cleanup_obj = c;
    if (cleanup_obj) {
        json_object_get(cleanup_obj);
    }
}

// TestResult类实现
TestResult::TestResult(const std::string& testId)
    : testId(testId), total(0), passed(0), failed(0), durationMs(0), caseResults(nullptr) {
    caseResults = json_object_new_array();
}

TestResult::~TestResult() {
    if (caseResults) {
        json_object_put(caseResults);
    }
}

void TestResult::addCaseResult(const std::string& caseName, bool passed, const std::string& message) {
    struct json_object* caseResult = json_object_new_object();
    
    json_object_object_add(caseResult, "name", json_object_new_string(caseName.c_str()));
    json_object_object_add(caseResult, "passed", json_object_new_boolean(passed));
    
    if (!message.empty()) {
        json_object_object_add(caseResult, "message", json_object_new_string(message.c_str()));
    }
    
    json_object_array_add(caseResults, caseResult);
    if (passed) {
        this->passed++;
    } else {
        this->failed++;
    }
    this->total++;
}

void TestResult::parseCaseListTask(std::shared_ptr<TestCaseListTask> task) {
    for (auto t : task->getSubTasks()) {
        if (t->getState() == Task::State::FINISHED) {
            addCaseResult(t->getName(), true);
        } else {
            addCaseResult(t->getName(), false, "Task failed");
        }
    }
}

void TestResult::setFinished(int total, int passed, int failed, int durationMs) {
    this->total = total;
    this->passed = passed;
    this->failed = failed;
    this->durationMs = durationMs;
}

bool TestResult::saveToFile(const std::string& filePath) const {
    struct json_object* result = json_object_new_object();
    
    json_object_object_add(result, "test_id", json_object_new_string(testId.c_str()));
    json_object_object_add(result, "total", json_object_new_int(total));
    json_object_object_add(result, "passed", json_object_new_int(passed));
    json_object_object_add(result, "failed", json_object_new_int(failed));
    json_object_object_add(result, "duration_ms", json_object_new_int(durationMs));
    json_object_object_add(result, "case_results", json_object_get(caseResults));
    
    const char* jsonStr = json_object_to_json_string_ext(result, JSON_C_TO_STRING_PRETTY);
    
    std::ofstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filePath << std::endl;
        json_object_put(result);
        return false;
    }
    
    file << jsonStr;
    file.close();
    
    json_object_put(result);
    return true;
}

TestSetupTask::TestSetupTask(const std::string& name, const std::string& description, struct json_object* setup_obj, Private)
    : Task(name, description, Private()), setup_obj(setup_obj) {
    if (setup_obj) {
        json_object_get(setup_obj);
    }

    // 创建子任务
    int setup_obj_length = json_object_array_length(setup_obj);
    for (int i = 0; i < setup_obj_length; i++) {
        struct json_object* setupStep = json_object_array_get_idx(setup_obj, i);
        struct json_object* actionObj;
        std::weak_ptr<Task> lastTask;
        if (json_object_object_get_ex(setupStep, "action", &actionObj)) {
            std::string action = json_object_get_string(actionObj);
            if (action == "exec") {
                struct json_object* commandObj;
                if (json_object_object_get_ex(setupStep, "command", &commandObj)) {
                    std::string command = json_object_get_string(commandObj);
                    auto t = TestExecTask::create("Exec", "", command);
                    t->setAfterTask(lastTask);
                    lastTask = addSubTask(t);
                }
            } else if (action == "wait") {
                int waitMs = 0;
                struct json_object* waitObj;
                if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                    waitMs = json_object_get_int(waitObj);
                }
                auto t = TestWaitTimeTask::create("Wait", "", waitMs);
                t->setAfterTask(lastTask);
                lastTask = addSubTask(t);
            } else if (action == "wait_attach") {
                int waitMs = 0;
                struct json_object* processesObj;
                if (json_object_object_get_ex(setupStep, "processes", &processesObj)) {
                    int length = json_object_array_length(processesObj);
                    std::vector<std::string> processes;
                    for (int i = 0; i < length; i++) {
                        struct json_object* processObj = json_object_array_get_idx(processesObj, i);
                        std::string process = json_object_get_string(processObj);
                        processes.push_back(process);
                    }
                    struct json_object* waitObj;
                    if (json_object_object_get_ex(setupStep, "wait_ms", &waitObj)) {
                        waitMs = json_object_get_int(waitObj);
                    }
                    auto t = TestWaitAttachTask::create("WaitAttach", "", processes, waitMs);
                    t->setAfterTask(lastTask);
                    lastTask = addSubTask(t);
                }
            }
        }
    }
}

TestSetupTask::~TestSetupTask() {
    if (setup_obj) {
        json_object_put(setup_obj);
    }
}

TestWaitAttachTask::TestWaitAttachTask(const std::string& name, const std::string& description, std::vector<std::string>& processes, int timeout_ms, Private)
    : Task(name, description, Private()), processes(processes) {
    if (timeout_ms > 0) {
        struct timespec now;
        clock_gettime(CLOCK_MONOTONIC, &now);
        end_time_s = now.tv_sec;
        end_time_ms = now.tv_nsec / 1000000;
        end_time_ms += timeout_ms;
        end_time_s += end_time_ms / 1000;
        end_time_ms %= 1000;
    }else{
        end_time_s = -1;
    }
}

TestWaitAttachTask::~TestWaitAttachTask() {
}

int TestWaitAttachTask::executeCustom() {
    if (state == State::INIT) {
        return 0;
    }
    if (state == State::RUNNING) {
        for (auto it = processes.begin(); it != processes.end();) {
            bool found = false;
            for (const auto& client : Context::getInstance().getClients()) {
                if (client->getProgramName() == *it) {
                    it = processes.erase(it);
                    found = true;
                    break;
                }
            }
            if (!found) ++it;
        }
        if (processes.empty()) {
            std::cout << "All processes attached" << std::endl;
            return 0;
        }
        if (end_time_s > 0) {
            struct timespec now;
            clock_gettime(CLOCK_MONOTONIC, &now);
            if (now.tv_sec > end_time_s || (now.tv_sec == end_time_s && now.tv_nsec / 1000000 > end_time_ms)) {
                std::cout << "Timeout waiting for processes" << std::endl;
                return -ETIMEDOUT;
            }
        }
        return -EAGAIN;
    }
    return 0;
}

TestWaitTimeTask::TestWaitTimeTask(const std::string& name, const std::string& description, int wait_ms, Private)
    : Task(name, description, Private()) {
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    end_time_s = now.tv_sec;
    end_time_ms = now.tv_nsec / 1000000;
    end_time_ms += wait_ms;
    end_time_s += end_time_ms / 1000;
    end_time_ms %= 1000;
}

TestWaitTimeTask::~TestWaitTimeTask() {
}

int TestWaitTimeTask::executeCustom() {
    if (state == State::INIT) {
        return 0;
    }
    if (state == State::RUNNING) {
        struct timespec now;
        clock_gettime(CLOCK_MONOTONIC, &now);
        if (now.tv_sec > end_time_s || (now.tv_sec == end_time_s && now.tv_nsec / 1000000 > end_time_ms)) {
            std::cout << "Time elapsed" << std::endl;
            return 0;
        }
    }
    return -EAGAIN;
}

TestCaseListTask::TestCaseListTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private)
    : SequentialTask(name, description, true, Private()), case_obj(case_obj) {
    if (case_obj) {
        json_object_get(case_obj);
    }
    // 创建子任务

    int casesCount = json_object_array_length(case_obj);
    std::string case_name;
    for (int i = 0; i < casesCount; i++) {
        struct json_object* caseObj = json_object_array_get_idx(case_obj, i);
        struct json_object* NameObj;
        if (json_object_object_get_ex(case_obj, "name", &NameObj)) {
            case_name = json_object_get_string(NameObj);
        }else{
            case_name = "Case " + std::to_string(i);
        }
        auto t = TestCaseTask::create(case_name, "", caseObj);
        addSubTask(t);
    }
}

TestCaseListTask::~TestCaseListTask() {
    if (case_obj) {
        json_object_put(case_obj);
    }
}

int TestCaseListTask::executeCustom() {
    //TODO
    return 0;
}

TestCaseTask::TestCaseTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private)
    : SequentialTask(name, description, false, Private()), case_obj(case_obj) {
    if (case_obj) {
        json_object_get(case_obj);
    }

    struct json_object* targetsObj;
    if (json_object_object_get_ex(case_obj, "targets", &targetsObj)) {
        int targetCount = json_object_array_length(targetsObj);
        for (int i = 0; i < targetCount; i++) {
            struct json_object* targetObj = json_object_array_get_idx(targetsObj, i);
            auto t = TestCaseRunTask::create("Run", "", targetObj);
            addSubTask(t);
        }
    }
    struct json_object* verifyObj;
    if (json_object_object_get_ex(case_obj, "verify", &verifyObj)) {
        auto t = TestCaseVerifyTask::create("Verify", "", verifyObj);
        addSubTask(t);
    }

}

TestCaseTask::~TestCaseTask() {
    if (case_obj) {
        json_object_put(case_obj);
    }
}

int TestCaseTask::executeCustom() {
    //TODO
    return 0;
}

TestCaseRunTask::TestCaseRunTask(const std::string& name, const std::string& description, struct json_object* case_obj, Private)
    : Task(name, description, Private()), case_obj(case_obj) {
    if (case_obj) {
        json_object_get(case_obj);
    }
}

TestCaseRunTask::~TestCaseRunTask() {
    if (case_obj) {
        json_object_put(case_obj);
    }
}

int TestCaseRunTask::executeCustom() {
    if (state == State::INIT) {
        // 发送请求前，先查找client，如果没有找到，则返回-EAGAIN
        if (client.expired())
        {
            struct json_object* programObj;
            std::string programName;
            if (json_object_object_get_ex(case_obj, "process", &programObj)) {
                programName = json_object_get_string(programObj);
            }else{
                return -EINVAL;
            }
            client = Context::getInstance().findClientByProgramName(programName);
            if (client.expired()) {
                return -EAGAIN;
            }

            std::shared_ptr<Task> lastTask = nullptr;
            
            // 发送aspects请求
            struct json_object* aspectsObj;
            std::vector<std::string> aspects = {};
            if (json_object_object_get_ex(case_obj, "aspects", &aspectsObj)) {
                int aspectsCount = json_object_array_length(aspectsObj);
                for (int i = 0; i < aspectsCount; i++) {
                    struct json_object* aspectObj = json_object_array_get_idx(aspectsObj, i);
                    std::string aspectName = json_object_get_string(aspectObj);
                    aspects.push_back(aspectName);
                }
                auto setAspectsTask = RequestTask::create("SetAspects", "", client, JsonProtocol::createAspectsRequest( aspects), -1);
                lastTask = setAspectsTask;
                addSubTask(setAspectsTask);
            }


            // 发送mocks请求
            struct json_object* mocksList;
            if (json_object_object_get_ex(case_obj, "mocks", &mocksList)) {

                struct json_object* mocksObj = json_object_new_object();
                json_object_object_add(mocksObj, "mocks", mocksList);
                auto mocksTask = RequestTask::create("Mocks", "", client, mocksObj, -1);
                json_object_put(mocksObj);
                mocksTask->setAfterTask(lastTask);
                addSubTask(mocksTask);
                lastTask = mocksTask;
            }

            // 发送mode请求
            auto modeTask = RequestTask::create("Mode", "", client, JsonProtocol::createSetModeRequest(TestMode::MOCK), -1);
            modeTask->setAfterTask(lastTask);
            addSubTask(modeTask);
            lastTask = modeTask;

            // 发送bkpt请求
            struct json_object* bkptListObj;
            if (json_object_object_get_ex(case_obj, "breakpoints", &bkptListObj)) {
                struct json_object* bkptObj = json_object_new_object();
                json_object_object_add(bkptObj, "breakpoints", bkptListObj);
                auto bkptTask = RequestTask::create("Breakpoints", "", client, bkptObj, -1);
                json_object_put(bkptObj);
                bkptTask->setAfterTask(lastTask);
                addSubTask(bkptTask);
                lastTask = bkptTask;
            }

            // 发送run请求
            auto actionTask = RequestTask::create("Action", "", client, JsonProtocol::createActionRequest("run"), -1);
            actionTask->setAfterTask(lastTask);
            addSubTask(actionTask);
            return -EAGAIN;
        }
        return 0;
    }
    if (state == State::RUNNING) {
        // 所有请求已发送
        return 0;
    }
    return 0;
}

TestCaseVerifyTask::TestCaseVerifyTask(const std::string& name, const std::string& description, struct json_object* verify_obj, Private)
    : Task(name, description, Private()), verify_obj(verify_obj) {
    if (verify_obj) {
        json_object_get(verify_obj);
    }
}

TestCaseVerifyTask::~TestCaseVerifyTask() {
    if (verify_obj) {
        json_object_put(verify_obj);
    }
}

int TestCaseVerifyTask::executeCustom() {
    if (state == State::INIT) {
        struct json_object* logObj;
        if (json_object_object_get_ex(verify_obj, "expect_debug_log", &logObj)) {
            expect_debug_log = true;
            struct json_object *logTypeObj, *logMessageObj;
            if (json_object_object_get_ex(logObj, "type", &logTypeObj) &&
                json_object_object_get_ex(logObj, "message", &logMessageObj)) {
                log_type = json_object_get_string(logTypeObj);
                log_message = json_object_get_string(logMessageObj);
            }else {
                std::cerr << "Missing 'type' or 'message' field in expect_debug_log" << std::endl;
                return -EINVAL;
            }
        }
        return 0;
    }
    if (state == State::RUNNING) {
        if (expect_debug_log) {
            // 检查日志队列中是否有匹配的消息
            for (const auto& client : Context::getInstance().getClients()) {
                auto messages = client->filterPendingMessages([&](const ClientMessageType& msg) -> bool{
                    if (msg.type == "log" && msg.getData()) {
                        if (msg.logType == log_type) {
                            return true;
                        }
                    }
                    return false;
                });
                if (!messages.empty()) {
                    for (const auto& msg : messages) {
                        std::string msg_str = json_object_get_string(msg.getData());
                        if (msg_str.find(log_message) != std::string::npos) {
                            return 0;
                        }else{
                            std::cerr << "Verify failed: expected log " << log_message
                                    << ", got " << msg_str << std::endl;
                            state = State::ERROR;
                            return 0;
                        }
                    }
                }
            }
        }
        return -EAGAIN;
    }
    return 0;
}

TestExecTask::TestExecTask(const std::string& name, const std::string& description, const std::string& command, Private)
    : Task(name, description, Private()), command(command) {
}

TestExecTask::~TestExecTask() {
}

int TestExecTask::executeCustom() {
    if (state == State::INIT) {
        pid_t pid;
        int status;
        char *argv[] = {
            const_cast<char*>("sh"), 
            const_cast<char*>("-c"), 
            const_cast<char*>(command.c_str()),
            nullptr
        };
        posix_spawn_file_actions_t file_actions;
        posix_spawn_file_actions_init(&file_actions);
        posix_spawn_file_actions_addchdir_np(&file_actions, "/data/git/testd/example");
        status = posix_spawn(&pid, "/bin/sh", &file_actions, NULL, argv, NULL);
        if (status != 0) {
            std::cerr << "Failed to exec" << std::endl;
            return -1;
        }
    }
    return 0;
}

} // namespace testd
